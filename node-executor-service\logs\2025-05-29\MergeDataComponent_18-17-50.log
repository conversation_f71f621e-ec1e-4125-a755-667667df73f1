2025-05-29 18:17:50 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-29\MergeDataComponent_18-17-50.log
2025-05-29 18:20:48 - MergeDataComponent - INFO - [__init__:96] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] MergeDataExecutor initialized
2025-05-29 18:20:48 - MergeDataComponent - INFO - [process:241] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Processing merge data request for request_id: e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - MergeDataComponent - INFO - [process:243] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:20:48 - MergeDataComponent - INFO - [process:257] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:20:48 - MergeDataComponent - INFO - [process:277] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Merging data for request_id e9348348-fcda-4afa-8907-1b38147bdaf6. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 18:20:48 - MergeDataComponent - ERROR - [process:308] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 18:23:14 - MergeDataComponent - INFO - [process:241] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing merge data request for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:14 - MergeDataComponent - INFO - [process:243] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:23:14 - MergeDataComponent - INFO - [process:257] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:23:14 - MergeDataComponent - INFO - [process:277] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Merging data for request_id 3e085dac-1e65-4de4-a1e6-952d7a69e387. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 18:23:14 - MergeDataComponent - ERROR - [process:308] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
