2025-05-29 18:35:46 - ApiRequestNode - INFO - [setup_logger:467] Logger ApiRequestNode configured with log file: logs\2025-05-29\ApiRequestNode_18-35-46.log
2025-05-29 18:35:46 - ApiRequestNode - INFO - [__init__:64] Initializing API Component
2025-05-29 18:35:46 - ApiRequestNode - INFO - [__init__:67] API Component initialized successfully
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:206] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Starting API request processing for request_id: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:224] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Extracting request new parameters https://www.postb.in/1748522981790-7621122791897, POST, {}, {} for request_id 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:232] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:234] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:240] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:260] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Final request body values - raw: {'technical': '12'}, json: None for request_id 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:267] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Request parameters extracted for request_id 9d055b4c-435f-4093-9468-609a423160f1: URL=https://www.postb.in/1748522981790-7621122791897, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:345] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748522981790-7621122791897, Timeout: Nones, RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:361] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:391] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748522981790-7621122791897 RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:392] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP BODY] None
2025-05-29 18:37:03 - ApiRequestNode - INFO - [process:393] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 18:37:04 - ApiRequestNode - INFO - [process:409] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP REQUEST COMPLETED] Duration: 0.827s, Status: 200, RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ApiRequestNode - INFO - [process:414] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748522981790-7621122791897, Method: POST, RequestID: 9d055b4c-435f-4093-9468-609a423160f1 
2025-05-29 18:37:04 - ApiRequestNode - INFO - [process:419] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 13:07:04 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-1lSaYwjxKDpqnjxSMuoWXIaoH+s\"",
  "X-Response-Time": "0.60330ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=Ggng%2Bg%2Bw3MR%2BUws1ZQODcxJ5JBh%2FMB6mDQZzmGoB6AR0tLDwRrvlrbpCKKZB4ZOlatGKb74e5EuX8hEYT7HEAAYvfzZt8D%2FJebYxg4JNgpVc4DqRQbEMZw%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9476366c4830e229-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ApiRequestNode - INFO - [process:430] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ApiRequestNode - INFO - [process:588] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE BODY] Text: 1748524023999-2398024837020, RequestID: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ApiRequestNode - INFO - [process:598] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] API request successful: Status=200, RequestID=9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:206] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Starting API request processing for request_id: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:224] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Extracting request new parameters https://www.postb.in/1748522981790-7621122791897, POST, {}, {} for request_id 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:232] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:234] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:240] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:260] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Final request body values - raw: {'technical': '12'}, json: None for request_id 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:267] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Request parameters extracted for request_id 64fc243c-52eb-4dc9-99cf-5176cf87c213: URL=https://www.postb.in/1748522981790-7621122791897, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:345] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748522981790-7621122791897, Timeout: Nones, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:361] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:391] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748522981790-7621122791897 RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:392] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP BODY] None
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:393] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:409] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP REQUEST COMPLETED] Duration: 0.674s, Status: 200, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:414] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748522981790-7621122791897, Method: POST, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213 
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:419] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 13:07:13 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-iSwEPViU8d5fn8oSWVOwfiVDGqU\"",
  "X-Response-Time": "0.66636ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=9gxK81BVoCZ1nMajsY%2FX219gGint20pjRAuXQ0Zx0Wq%2Fs9sbRabqSZYx0EPwZyg8qGDGY6VhrAXD3MINxSN1Grq3ho%2F7y6iNNq%2F3gBEKXBwDAxjt%2FP%2FqQw%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "947636a5cde2e191-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:430] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:588] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] [HTTP RESPONSE BODY] Text: 1748524033199-1356876087374, RequestID: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ApiRequestNode - INFO - [process:598] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] API request successful: Status=200, RequestID=64fc243c-52eb-4dc9-99cf-5176cf87c213
