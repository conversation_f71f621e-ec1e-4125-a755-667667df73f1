2025-05-29 18:35:45 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-29\ComponentSystem_18-35-45.log
2025-05-29 18:35:45 - ComponentSystem - INFO - [get_component_manager:1393] Creating new global ComponentManager instance
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-29 18:35:45 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-29 18:35:45 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-29 18:35:45 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-29 18:35:45 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-29 18:35:45 - ComponentSystem - INFO - [discover_component_modules:1332] Discovering component modules
2025-05-29 18:35:45 - ComponentSystem - INFO - [discover_component_modules:1348] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-29 18:35:45 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.alter_metadata_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.alter_metadata_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.api_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.api_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.combine_text_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.combine_text_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.combine_text_component_new
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.combine_text_component_new
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.convert_script_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.convert_script_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.data_to_dataframe_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.doc_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.doc_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.dynamic_combine_text_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.gmail_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.gmail_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.gmail_tracker_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.gmail_tracker_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.id_generator_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.id_generator_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.merge_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-29 18:35:46 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.merge_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.message_to_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.message_to_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.select_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.select_data_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.split_text_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.split_text_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.text_analysis_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.text_analysis_component
2025-05-29 18:35:46 - ComponentSystem - INFO - [discover_component_modules:1371] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-29 18:35:46 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-29 18:35:52 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-29 18:35:52 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-29 18:35:52 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-29 18:35:52 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=354, TaskID=ApiRequestNode-node-execution-request-0-354-1748523952.836838
2025-05-29 18:35:52 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-354-1748523952.836838, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "3e085dac-1e65-4de4-a1e6-952d7a69e387",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:35:52 - ComponentSystem - INFO - [_process_message:713] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool MergeDataComponent for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387, TaskID=ApiRequestNode-node-execution-request-0-354-1748523952.836838
2025-05-29 18:35:52 - ComponentSystem - INFO - [_process_message:717] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool MergeDataComponent executed successfully for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387, TaskID=ApiRequestNode-node-execution-request-0-354-1748523952.836838
2025-05-29 18:35:52 - ComponentSystem - INFO - [_send_result:1005] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Preparing to send result for component ApiRequestNode, RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:52 - ComponentSystem - INFO - [get_producer:244] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 18:35:52 - ComponentSystem - INFO - [get_producer:247] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91]   Bootstrap Servers: **************:9092
2025-05-29 18:35:52 - ComponentSystem - INFO - [get_producer:248] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 18:35:52 - ComponentSystem - INFO - [get_producer:252] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91]   Request Timeout: 60000ms
2025-05-29 18:35:52 - ComponentSystem - INFO - [get_producer:255] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 18:35:52 - ComponentSystem - INFO - [get_producer:259] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 18:35:54 - ComponentSystem - INFO - [get_producer:266] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 18:35:54 - ComponentSystem - INFO - [_send_result:1037] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component returned error status for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387: Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 18:35:54 - ComponentSystem - INFO - [_send_result:1084] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sending Kafka response: RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387, Response={
  "request_id": "3e085dac-1e65-4de4-a1e6-952d7a69e387",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748523954.7854395,
  "result": null,
  "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
}
2025-05-29 18:35:55 - ComponentSystem - INFO - [_send_result:1093] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sent result for component ApiRequestNode to topic node_results for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:55 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully committed offset 355 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-354-1748523952.836838
2025-05-29 18:35:55 - ComponentSystem - INFO - [_process_message:936] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=354, TaskID=ApiRequestNode-node-execution-request-0-354-1748523952.836838
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=355, TaskID=ApiRequestNode-node-execution-request-0-355-1748524023.6583385
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-355-1748524023.6583385, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "9c52d8cc-ebfb-4285-ac80-966f05123db0",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:713] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool CombineTextComponent for RequestID=9c52d8cc-ebfb-4285-ac80-966f05123db0, TaskID=ApiRequestNode-node-execution-request-0-355-1748524023.6583385
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:717] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool CombineTextComponent executed successfully for RequestID=9c52d8cc-ebfb-4285-ac80-966f05123db0, TaskID=ApiRequestNode-node-execution-request-0-355-1748524023.6583385
2025-05-29 18:37:03 - ComponentSystem - INFO - [_send_result:1005] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Preparing to send result for component ApiRequestNode, RequestID=9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:03 - ComponentSystem - INFO - [_send_result:1084] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sending Kafka response: RequestID=9c52d8cc-ebfb-4285-ac80-966f05123db0, Response={
  "request_id": "9c52d8cc-ebfb-4285-ac80-966f05123db0",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748524023.6667886,
  "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}",
  "error": null
}
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=356, TaskID=ApiRequestNode-node-execution-request-0-356-1748524023.6583385
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-356-1748524023.6583385, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "9d055b4c-435f-4093-9468-609a423160f1",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:03 - ComponentSystem - INFO - [_process_message:713] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool ApiRequestNode for RequestID=9d055b4c-435f-4093-9468-609a423160f1, TaskID=ApiRequestNode-node-execution-request-0-356-1748524023.6583385
2025-05-29 18:37:03 - ComponentSystem - INFO - [_send_result:1093] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sent result for component ApiRequestNode to topic node_results for RequestID=9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:04 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Successfully committed offset 356 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-355-1748524023.6583385
2025-05-29 18:37:04 - ComponentSystem - INFO - [_process_message:936] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=355, TaskID=ApiRequestNode-node-execution-request-0-355-1748524023.6583385
2025-05-29 18:37:04 - ComponentSystem - INFO - [_process_message:717] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool ApiRequestNode executed successfully for RequestID=9d055b4c-435f-4093-9468-609a423160f1, TaskID=ApiRequestNode-node-execution-request-0-356-1748524023.6583385
2025-05-29 18:37:04 - ComponentSystem - INFO - [_send_result:1005] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Preparing to send result for component ApiRequestNode, RequestID=9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ComponentSystem - INFO - [_send_result:1084] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sending Kafka response: RequestID=9d055b4c-435f-4093-9468-609a423160f1, Response={
  "request_id": "9d055b4c-435f-4093-9468-609a423160f1",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748524024.5138736,
  "result": {
    "status_code": 200,
    "headers": {
      "Date": "Thu, 29 May 2025 13:07:04 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-1lSaYwjxKDpqnjxSMuoWXIaoH+s\"",
      "X-Response-Time": "0.60330ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=Ggng%2Bg%2Bw3MR%2BUws1ZQODcxJ5JBh%2FMB6mDQZzmGoB6AR0tLDwRrvlrbpCKKZB4ZOlatGKb74e5EuX8hEYT7HEAAYvfzZt8D%2FJebYxg4JNgpVc4DqRQbEMZw%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9476366c4830e229-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "content_type": "text/plain",
    "data": "1748524023999-2398024837020",
    "method": "POST",
    "url": "https://www.postb.in/1748522981790-7621122791897"
  },
  "error": null
}
2025-05-29 18:37:04 - ComponentSystem - INFO - [_send_result:1093] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sent result for component ApiRequestNode to topic node_results for RequestID=9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:05 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Successfully committed offset 357 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-356-1748524023.6583385
2025-05-29 18:37:05 - ComponentSystem - INFO - [_process_message:936] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=356, TaskID=ApiRequestNode-node-execution-request-0-356-1748524023.6583385
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=357, TaskID=ApiRequestNode-node-execution-request-0-357-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-357-1748524033.0119772, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "5bcf3662-e4f8-4584-a5e6-1304252b5aa4",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:713] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool MergeDataComponent for RequestID=5bcf3662-e4f8-4584-a5e6-1304252b5aa4, TaskID=ApiRequestNode-node-execution-request-0-357-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:717] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool MergeDataComponent executed successfully for RequestID=5bcf3662-e4f8-4584-a5e6-1304252b5aa4, TaskID=ApiRequestNode-node-execution-request-0-357-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1005] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Preparing to send result for component ApiRequestNode, RequestID=5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1037] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Component returned error status for RequestID=5bcf3662-e4f8-4584-a5e6-1304252b5aa4: Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1084] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sending Kafka response: RequestID=5bcf3662-e4f8-4584-a5e6-1304252b5aa4, Response={
  "request_id": "5bcf3662-e4f8-4584-a5e6-1304252b5aa4",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748524033.0207033,
  "result": null,
  "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
}
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=358, TaskID=ApiRequestNode-node-execution-request-0-358-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-358-1748524033.0119772, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "64fc243c-52eb-4dc9-99cf-5176cf87c213",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:713] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool ApiRequestNode for RequestID=64fc243c-52eb-4dc9-99cf-5176cf87c213, TaskID=ApiRequestNode-node-execution-request-0-358-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1093] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sent result for component ApiRequestNode to topic node_results for RequestID=5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Successfully committed offset 358 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-357-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:936] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=357, TaskID=ApiRequestNode-node-execution-request-0-357-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_process_message:717] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool ApiRequestNode executed successfully for RequestID=64fc243c-52eb-4dc9-99cf-5176cf87c213, TaskID=ApiRequestNode-node-execution-request-0-358-1748524033.0119772
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1005] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Preparing to send result for component ApiRequestNode, RequestID=64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1084] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sending Kafka response: RequestID=64fc243c-52eb-4dc9-99cf-5176cf87c213, Response={
  "request_id": "64fc243c-52eb-4dc9-99cf-5176cf87c213",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748524033.712385,
  "result": {
    "status_code": 200,
    "headers": {
      "Date": "Thu, 29 May 2025 13:07:13 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-iSwEPViU8d5fn8oSWVOwfiVDGqU\"",
      "X-Response-Time": "0.66636ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=9gxK81BVoCZ1nMajsY%2FX219gGint20pjRAuXQ0Zx0Wq%2Fs9sbRabqSZYx0EPwZyg8qGDGY6VhrAXD3MINxSN1Grq3ho%2F7y6iNNq%2F3gBEKXBwDAxjt%2FP%2FqQw%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "947636a5cde2e191-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "content_type": "text/plain",
    "data": "1748524033199-1356876087374",
    "method": "POST",
    "url": "https://www.postb.in/1748522981790-7621122791897"
  },
  "error": null
}
2025-05-29 18:37:13 - ComponentSystem - INFO - [_send_result:1093] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Sent result for component ApiRequestNode to topic node_results for RequestID=64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:14 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Successfully committed offset 359 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-358-1748524033.0119772
2025-05-29 18:37:14 - ComponentSystem - INFO - [_process_message:936] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=358, TaskID=ApiRequestNode-node-execution-request-0-358-1748524033.0119772
2025-05-29 18:47:53 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-29 18:47:53 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-29 18:47:53 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-29 18:47:53 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
