2025-05-29 18:17:50 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs\2025-05-29\ToolExecutor_18-17-50.log
2025-05-29 18:17:50 - ToolExecutor - INFO - [setup_tool_executor_logger:97] <PERSON><PERSON><PERSON> logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-05-29 18:17:50 - ToolExecutor - INFO - [get_tool_executor:187] Creating new global ToolExecutor instance
2025-05-29 18:17:50 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-05-29 18:17:50 - ToolExecutor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-05-29 18:18:59 - ToolExecutor - INFO - [execute_tool:94] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Executing tool for request_id: 567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:18:59 - ToolExecutor - INFO - [execute_tool:97] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "mine",
    "num_additional_inputs": "1",
    "separator": ",",
    "input_1": "hello",
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "567a332e-2596-4b1e-950c-00c1cd66f29e",
  "correlation_id": "75a09b80-8b81-47a1-8a6b-91c12794c399"
}
2025-05-29 18:18:59 - ToolExecutor - INFO - [execute_tool:111] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Tool name: CombineTextComponent for request_id: 567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:18:59 - ToolExecutor - INFO - [execute_tool:139] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Processing payload with component CombineTextComponent for request_id: 567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:18:59 - ToolExecutor - INFO - [execute_tool:143] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Component CombineTextComponent processed payload successfully for request_id: 567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:18:59 - ToolExecutor - INFO - [execute_tool:149] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] ToolExecutor returning raw component result for request_id: 567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:94] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool for request_id: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:97] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "d7aa3f75-76dd-41ac-8393-38ffba611af5",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:111] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool name: ApiRequestNode for request_id: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:139] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Processing payload with component ApiRequestNode for request_id: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:94] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool for request_id: 087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:97] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "087bb3c2-28fc-4332-bd79-4961b43439c5",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:111] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool name: CombineTextComponent for request_id: 087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:139] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Processing payload with component CombineTextComponent for request_id: 087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:143] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Component CombineTextComponent processed payload successfully for request_id: 087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:39 - ToolExecutor - INFO - [execute_tool:149] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor returning raw component result for request_id: 087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:40 - ToolExecutor - INFO - [execute_tool:143] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Component ApiRequestNode processed payload successfully for request_id: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ToolExecutor - INFO - [execute_tool:149] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor returning raw component result for request_id: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:94] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool for request_id: e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:97] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "e9348348-fcda-4afa-8907-1b38147bdaf6",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:111] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool name: MergeDataComponent for request_id: e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:139] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Processing payload with component MergeDataComponent for request_id: e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:143] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Component MergeDataComponent processed payload successfully for request_id: e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:149] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor returning raw component result for request_id: e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:94] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool for request_id: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:97] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "46ec8cd9-4d77-4f21-bbe8-c55c7e231620",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:111] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool name: ApiRequestNode for request_id: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ToolExecutor - INFO - [execute_tool:139] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Processing payload with component ApiRequestNode for request_id: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ToolExecutor - INFO - [execute_tool:143] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Component ApiRequestNode processed payload successfully for request_id: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ToolExecutor - INFO - [execute_tool:149] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] ToolExecutor returning raw component result for request_id: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:94] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool for request_id: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:97] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "3267d8bb-feef-42c6-8be9-4fee612c841e",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:111] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool name: ApiRequestNode for request_id: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:139] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing payload with component ApiRequestNode for request_id: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:94] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool for request_id: 04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:97] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "04bc3144-7941-4b41-8241-17d832c66a02",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:111] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool name: CombineTextComponent for request_id: 04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:139] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing payload with component CombineTextComponent for request_id: 04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:143] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component CombineTextComponent processed payload successfully for request_id: 04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:06 - ToolExecutor - INFO - [execute_tool:149] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor returning raw component result for request_id: 04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:07 - ToolExecutor - INFO - [execute_tool:143] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component ApiRequestNode processed payload successfully for request_id: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:07 - ToolExecutor - INFO - [execute_tool:149] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor returning raw component result for request_id: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:94] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool for request_id: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:97] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "eca069e4-6916-485a-a9d1-9f31358f16c2",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:111] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool name: ApiRequestNode for request_id: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:139] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing payload with component ApiRequestNode for request_id: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:94] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:97] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "3e085dac-1e65-4de4-a1e6-952d7a69e387",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:111] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool name: MergeDataComponent for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:139] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing payload with component MergeDataComponent for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:143] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component MergeDataComponent processed payload successfully for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:14 - ToolExecutor - INFO - [execute_tool:149] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor returning raw component result for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:15 - ToolExecutor - INFO - [execute_tool:143] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component ApiRequestNode processed payload successfully for request_id: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:15 - ToolExecutor - INFO - [execute_tool:149] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor returning raw component result for request_id: eca069e4-6916-485a-a9d1-9f31358f16c2
