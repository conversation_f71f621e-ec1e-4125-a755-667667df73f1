2025-05-29 18:35:46 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs\2025-05-29\ToolExecutor_18-35-46.log
2025-05-29 18:35:46 - ToolExecutor - INFO - [setup_tool_executor_logger:97] <PERSON><PERSON><PERSON> logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-05-29 18:35:46 - ToolExecutor - INFO - [get_tool_executor:187] Creating new global ToolExecutor instance
2025-05-29 18:35:46 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-05-29 18:35:46 - Too<PERSON>Executor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-05-29 18:35:52 - ToolExecutor - INFO - [execute_tool:94] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:52 - ToolExecutor - INFO - [execute_tool:97] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "3e085dac-1e65-4de4-a1e6-952d7a69e387",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:35:52 - ToolExecutor - INFO - [execute_tool:111] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool name: MergeDataComponent for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:52 - ToolExecutor - INFO - [execute_tool:139] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing payload with component MergeDataComponent for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:52 - ToolExecutor - INFO - [execute_tool:143] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component MergeDataComponent processed payload successfully for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:52 - ToolExecutor - INFO - [execute_tool:149] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] ToolExecutor returning raw component result for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:94] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool for request_id: 9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:97] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "9c52d8cc-ebfb-4285-ac80-966f05123db0",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:111] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool name: CombineTextComponent for request_id: 9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:139] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Processing payload with component CombineTextComponent for request_id: 9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:143] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Component CombineTextComponent processed payload successfully for request_id: 9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:149] [ReqID:9c52d8cc-ebfb-4285-ac80-966f05123db0] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor returning raw component result for request_id: 9c52d8cc-ebfb-4285-ac80-966f05123db0
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:94] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool for request_id: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:97] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "9d055b4c-435f-4093-9468-609a423160f1",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:111] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool name: ApiRequestNode for request_id: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:03 - ToolExecutor - INFO - [execute_tool:139] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Processing payload with component ApiRequestNode for request_id: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ToolExecutor - INFO - [execute_tool:143] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Component ApiRequestNode processed payload successfully for request_id: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:04 - ToolExecutor - INFO - [execute_tool:149] [ReqID:9d055b4c-435f-4093-9468-609a423160f1] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor returning raw component result for request_id: 9d055b4c-435f-4093-9468-609a423160f1
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:94] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool for request_id: 5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:97] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "5bcf3662-e4f8-4584-a5e6-1304252b5aa4",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:111] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool name: MergeDataComponent for request_id: 5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:139] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Processing payload with component MergeDataComponent for request_id: 5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:143] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Component MergeDataComponent processed payload successfully for request_id: 5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:149] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor returning raw component result for request_id: 5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:94] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Executing tool for request_id: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:97] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "64fc243c-52eb-4dc9-99cf-5176cf87c213",
  "correlation_id": "89e274bb-35e0-4aad-ae09-ce80cb48aaa2"
}
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:111] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Tool name: ApiRequestNode for request_id: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:139] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Processing payload with component ApiRequestNode for request_id: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:143] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Component ApiRequestNode processed payload successfully for request_id: 64fc243c-52eb-4dc9-99cf-5176cf87c213
2025-05-29 18:37:13 - ToolExecutor - INFO - [execute_tool:149] [ReqID:64fc243c-52eb-4dc9-99cf-5176cf87c213] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] ToolExecutor returning raw component result for request_id: 64fc243c-52eb-4dc9-99cf-5176cf87c213
