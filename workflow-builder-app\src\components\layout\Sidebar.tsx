import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { ComponentsApiResponse, ComponentDefinition } from "@/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  LogIn,
  Grip,
  Brain,
  Cloud,
  Store,
} from "lucide-react";

interface SidebarProps {
  components: ComponentsApiResponse;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

// Helper function to make a node draggable
const onDragStart = (event: React.DragEvent, nodeType: string, definition: ComponentDefinition) => {
  const nodeData = JSON.stringify({ nodeType, definition });
  event.dataTransfer.setData("application/reactflow", nodeData);
  event.dataTransfer.effectAllowed = "move";
};

// Helper function to get appropriate icon based on category
const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return LogIn;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    case "ai":
      return Brain; // AI components use Brain icon
    case "mcp":
      return Cloud; // MCP components use Cloud icon
    case "marketplace":
      return Store; // Marketplace components use Store icon
    default:
      return Cog;
  }
};

// Helper function to get user-friendly category display name
const getCategoryDisplayName = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return "Input/Output";
    case "ai":
      return "AI/LLM";
    case "mcp":
      return "MCP Marketplace";
    default:
      return category;
  }
};

export const Sidebar = React.memo(function Sidebar({
  components,
  collapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  // Remove all console.log statements to prevent unnecessary work during renders
  
  console.log("Sidebar component categories:", Object.keys(components));

  // Check if MCP category exists and log its contents
  if (components.MCP) {
    console.log("MCP category exists with components:", Object.keys(components.MCP));
  } else {
    console.log("MCP category does not exist in components");
  }
  const categories = Object.keys(components).sort();

  const [searchTerm, setSearchTerm] = useState("");
  // Initialize with IO, AI, Data, and MCP categories expanded by default
  const [expandedCategories, setExpandedCategories] = useState<string[]>(
    categories.filter((cat) => ["io", "ai", "data", "mcp"].includes(cat.toLowerCase())),
  );

  // Filter components based on search term
  const filteredComponents = useCallback(() => {
    if (!searchTerm.trim()) {
      return components;
    }

    const filtered: ComponentsApiResponse = {};
    const searchLower = searchTerm.toLowerCase();

    Object.entries(components).forEach(([category, categoryComponents]) => {
      const matchingComponents = Object.values(categoryComponents).filter(
        (comp) =>
          comp.display_name.toLowerCase().includes(searchLower) ||
          comp.description.toLowerCase().includes(searchLower),
      );

      if (matchingComponents.length > 0) {
        filtered[category] = Object.fromEntries(
          matchingComponents.map((comp) => [comp.name, comp]),
        );
      }
    });

    return filtered;
  }, [components, searchTerm]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Auto-expand all categories when searching
    if (value.trim()) {
      setExpandedCategories(Object.keys(filteredComponents()));
    }
  };

  // Toggle category expansion
  const handleCategoryToggle = (category: string) => {
    setExpandedCategories((prev) =>
      prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category],
    );
  };

  // Memoize the filtered data to prevent recalculation on every render
  const filteredData = useMemo(() => filteredComponents(), [filteredComponents]);

  // Memoize the visible categories to prevent recalculation on every render
  const visibleCategories = useMemo(() => {
    // Custom sort function to prioritize IO, AI, MCP, and Data categories
    return Object.keys(filteredData).sort((a, b) => {
      // Priority order: IO first, then AI, then MCP, then Data, then alphabetical
      if (a.toLowerCase() === "io") return -1;
      if (b.toLowerCase() === "io") return 1;
      if (a.toLowerCase() === "ai") return -1;
      if (b.toLowerCase() === "ai") return 1;
      if (a.toLowerCase() === "mcp") return -1;
      if (b.toLowerCase() === "mcp") return 1;
      if (a.toLowerCase() === "data") return -1;
      if (b.toLowerCase() === "data") return 1;
      return a.localeCompare(b);
    });
  }, [filteredData]);

  return (
    <aside
      className={`bg-sidebar border-brand-stroke relative flex h-full shrink-0 flex-col overflow-hidden border-r shadow-md transition-all duration-300 ${
        collapsed ? "w-16" : "w-80"
      }`}
    >
      {/* Theme-responsive overlay */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-black/5 dark:bg-black/20"></div>

      {/* Removed the border button */}

      <div
        className={`border-brand-border-color brand-gradient-indicator relative z-10 flex-shrink-0 border-b ${
          collapsed ? "p-3" : "p-5"
        }`}
      >
        {!collapsed && (
          <div className="relative flex items-center">
            <div className="relative flex-grow">
              <Search className="text-brand-secondary absolute top-3 left-3 h-5 w-5" />
              <Input
                placeholder="Search components..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="border-brand-stroke text-brand-primary-font placeholder:text-brand-secondary-font focus-visible:ring-brand-primary/30 dark:bg-brand-card dark:text-brand-white-text dark:placeholder:text-brand-secondary-font h-11 rounded-md bg-white/90 pl-10 text-sm"
              />
            </div>
            <button
              onClick={onToggleCollapse}
              className="text-brand-primary hover:text-brand-primary hover:bg-brand-card-hover dark:text-brand-secondary dark:hover:text-brand-secondary dark:hover:bg-brand-clicked ml-2 flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:shadow-md"
              aria-label="Collapse sidebar"
              title="Collapse sidebar (Alt+S)"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15 6L9 12L15 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        )}
        {collapsed && (
          <div className="flex items-center justify-center">
            <button
              onClick={onToggleCollapse}
              className="text-brand-primary hover:text-brand-primary hover:bg-brand-card-hover dark:text-brand-secondary dark:hover:text-brand-secondary dark:hover:bg-brand-clicked flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:shadow-md"
              aria-label="Expand sidebar"
              title="Expand sidebar (Alt+S)"
            >
              {/* Hamburger menu icon */}
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3 12H21M3 6H21M3 18H21"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      <CustomScrollArea className="custom-scrollbar relative z-10 flex-grow">
        {!collapsed ? (
          <>
            <Accordion
              type="multiple"
              className="w-full space-y-3 px-5 py-4"
              value={expandedCategories}
            >
              {visibleCategories.map((category) => {
                const CategoryIcon = getCategoryIcon(category);
                return (
                  <AccordionItem
                    value={category}
                    key={category}
                    className={`border ${
                      category.toLowerCase() === "io" || category.toLowerCase() === "data"
                        ? "border-brand-primary/30 bg-brand-card-hover dark:border-brand-primary/40 dark:bg-brand-card"
                        : "border-brand-stroke dark:border-brand-stroke dark:bg-brand-card/80 bg-white/90"
                    } overflow-hidden rounded-lg shadow-md`}
                  >
                    <AccordionTrigger
                      className="font-primary hover:bg-brand-card-hover dark:hover:bg-brand-clicked px-4 py-4 text-base font-semibold transition-all duration-200 hover:no-underline"
                      onClick={() => handleCategoryToggle(category)}
                    >
                      <div className="flex w-full items-center gap-2">
                        <CategoryIcon className="text-brand-primary dark:text-brand-secondary h-6 w-6" />
                        <span className="text-brand-primary-font dark:text-brand-white-text">
                          {getCategoryDisplayName(category)}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="accordion-content-animation px-3 pb-4">
                      <div className="max-h-[300px] space-y-4 overflow-y-auto pt-3 pr-2">
                        {Object.values(filteredData[category])
                          .sort((a, b) => a.display_name.localeCompare(b.display_name))
                          .map((compDef) => (
                            <div
                              key={compDef.name}
                              className="group border-brand-stroke hover:border-brand-primary/30 hover:bg-brand-card-hover dark:border-brand-stroke dark:bg-brand-card dark:hover:border-brand-primary/40 dark:hover:bg-brand-clicked relative cursor-grab rounded-md border bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:shadow-lg"
                              onDragStart={(event) => onDragStart(event, compDef.name, compDef)}
                              draggable
                            >
                              <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                              </div>
                              <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                <span className="text-brand-primary group-hover:text-brand-primary/80 dark:text-brand-secondary dark:group-hover:text-brand-secondary/80 text-sm transition-colors">
                                  {compDef.display_name}
                                </span>
                                {compDef.type === "MCPMarketplaceComponent" && (
                                  <Badge className="ml-1 bg-blue-500 text-[10px] dark:bg-blue-600">
                                    MCP
                                  </Badge>
                                )}
                              </div>
                              <div className="border-brand-stroke dark:border-brand-stroke/50 mb-2 border-t pt-2"></div>
                              <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                {compDef.description}
                              </p>
                            </div>
                          ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>

            {visibleCategories.length === 0 && (
              <div className="border-brand-stroke bg-brand-card-hover text-brand-secondary-font dark:border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font m-4 rounded-lg border p-6 text-center text-sm">
                <Search className="text-brand-primary/70 dark:text-brand-secondary/70 mx-auto mb-3 h-6 w-6" />
                No components match your search.
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center gap-3 px-1 py-6">
            {visibleCategories.map((category) => {
              const CategoryIcon = getCategoryIcon(category);
              return (
                <div
                  key={category}
                  className="group mb-1 flex flex-col items-center"
                  title={getCategoryDisplayName(category)}
                >
                  <button
                    className={`flex h-10 w-10 items-center justify-center rounded-full shadow-sm ${
                      category.toLowerCase() === "io" || category.toLowerCase() === "data"
                        ? "bg-brand-primary/10 text-brand-primary dark:bg-brand-primary/20 dark:text-brand-secondary"
                        : "bg-brand-card-hover text-brand-primary-font dark:bg-brand-card dark:text-brand-white-text"
                    } transition-all hover:scale-110 hover:shadow-md`}
                    onClick={() => {
                      if (onToggleCollapse) onToggleCollapse();
                      setTimeout(() => handleCategoryToggle(category), 300);
                    }}
                    aria-label={`Open ${getCategoryDisplayName(category)} category`}
                  >
                    <CategoryIcon className="h-5 w-5" />
                  </button>
                  <span className="text-brand-secondary-font mt-1 text-[10px] font-medium opacity-80">
                    {getCategoryDisplayName(category).substring(0, 3)}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </CustomScrollArea>
    </aside>
  );
});
