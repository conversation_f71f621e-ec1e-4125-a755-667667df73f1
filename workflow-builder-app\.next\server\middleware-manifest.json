{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "643b9e08c78920171a69ddaf328106a6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "584990d6e601d79284532905ae5bdce13821e9acb5d02078699ad65653051ab8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "494b0d343865b5b919462419194cd67f1ac066a763bb3426d455ff82495419db"}}}, "sortedMiddleware": ["/"], "functions": {}}