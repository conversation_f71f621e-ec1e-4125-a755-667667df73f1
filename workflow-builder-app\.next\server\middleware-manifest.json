{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "aa455ed1cfddb44b26ec07d43cda4844", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5c92b5d77d8dcc862546b7dcc579f7a2062445fae7a31ed2c03577dde8843008", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b55b7dfe23a71fb7e6c3f5be41e604e2d5bf78e0755d1e7d1878b0e1e2ca1db7"}}}, "sortedMiddleware": ["/"], "functions": {}}