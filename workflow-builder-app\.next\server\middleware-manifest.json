{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "906b2f40586a5c9195e529a4f98520be", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e9e605b7c06e6f261ccabae981e1506c1aabaaea4423c1663248aebcb5b5fc57", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4d89b1b1287cf87c850c29e30e94e7c4e9fff69de1b22bac8595450e09fe4ce5"}}}, "sortedMiddleware": ["/"], "functions": {}}