{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "59e124ceecb1ffdd1e98a5948c028a58", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d043abe0273a1bac341909e236dfc7d6e49a8bd0aa75f00ece2e4e77187fda09", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0b3b0a2ed3c656d87bb7ce9a306cdff4a8a28299f186b135e35df6bc7d185fb4"}}}, "sortedMiddleware": ["/"], "functions": {}}