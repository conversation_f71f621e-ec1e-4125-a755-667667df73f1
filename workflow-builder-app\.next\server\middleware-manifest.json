{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "f9edee5fe8c4b4189c82e6de0770dfb3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e9d15d8d8f2f50489c55febc0e535702df099fbfade0157cffbf214e125c4136", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "90a7ca81eb006d39968646b4dfb2476f12795adade2a8216d677754196c27c20"}}}, "sortedMiddleware": ["/"], "functions": {}}