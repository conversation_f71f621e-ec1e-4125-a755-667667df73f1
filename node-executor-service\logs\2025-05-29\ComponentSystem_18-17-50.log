2025-05-29 18:17:50 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-29\ComponentSystem_18-17-50.log
2025-05-29 18:17:50 - ComponentSystem - INFO - [get_component_manager:1386] Creating new global ComponentManager instance
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-29 18:17:50 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1325] Discovering component modules
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1341] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.alter_metadata_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.alter_metadata_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.api_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.api_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.combine_text_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.combine_text_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.combine_text_component_new
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.combine_text_component_new
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.convert_script_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.convert_script_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.data_to_dataframe_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.doc_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.doc_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.dynamic_combine_text_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.gmail_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.gmail_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.gmail_tracker_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.gmail_tracker_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.id_generator_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.id_generator_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.merge_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-29 18:17:50 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.merge_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.message_to_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.message_to_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.select_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.select_data_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.split_text_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.split_text_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1351] Importing component module: app.components.text_analysis_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1355] Successfully imported component module: app.components.text_analysis_component
2025-05-29 18:17:50 - ComponentSystem - INFO - [discover_component_modules:1364] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-29 18:17:50 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-29 18:17:56 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-29 18:17:56 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-29 18:17:56 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-29 18:18:59 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=346, TaskID=ApiRequestNode-node-execution-request-0-346-1748522939.8225026
2025-05-29 18:18:59 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-346-1748522939.8225026, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "mine",
    "num_additional_inputs": "1",
    "separator": ",",
    "input_1": "hello",
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "567a332e-2596-4b1e-950c-00c1cd66f29e",
  "correlation_id": "75a09b80-8b81-47a1-8a6b-91c12794c399"
}
2025-05-29 18:18:59 - ComponentSystem - INFO - [_process_message:713] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Executing tool CombineTextComponent for RequestID=567a332e-2596-4b1e-950c-00c1cd66f29e, TaskID=ApiRequestNode-node-execution-request-0-346-1748522939.8225026
2025-05-29 18:18:59 - ComponentSystem - INFO - [_process_message:717] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Tool CombineTextComponent executed successfully for RequestID=567a332e-2596-4b1e-950c-00c1cd66f29e, TaskID=ApiRequestNode-node-execution-request-0-346-1748522939.8225026
2025-05-29 18:18:59 - ComponentSystem - INFO - [_send_result:1005] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Preparing to send result for component ApiRequestNode, RequestID=567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:18:59 - ComponentSystem - INFO - [get_producer:244] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 18:18:59 - ComponentSystem - INFO - [get_producer:247] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399]   Bootstrap Servers: **************:9092
2025-05-29 18:18:59 - ComponentSystem - INFO - [get_producer:248] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 18:18:59 - ComponentSystem - INFO - [get_producer:252] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399]   Request Timeout: 60000ms
2025-05-29 18:18:59 - ComponentSystem - INFO - [get_producer:255] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 18:18:59 - ComponentSystem - INFO - [get_producer:259] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 18:19:01 - ComponentSystem - INFO - [get_producer:266] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 18:19:01 - ComponentSystem - INFO - [_send_result:1077] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Sending Kafka response: RequestID=567a332e-2596-4b1e-950c-00c1cd66f29e, Response={
  "request_id": "567a332e-2596-4b1e-950c-00c1cd66f29e",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748522941.8127832,
  "result": {
    "result": "mine,hello"
  },
  "error": null
}
2025-05-29 18:19:02 - ComponentSystem - INFO - [_send_result:1086] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Sent result for component ApiRequestNode to topic node_results for RequestID=567a332e-2596-4b1e-950c-00c1cd66f29e
2025-05-29 18:19:02 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Successfully committed offset 347 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-346-1748522939.8225026
2025-05-29 18:19:02 - ComponentSystem - INFO - [_process_message:936] [ReqID:567a332e-2596-4b1e-950c-00c1cd66f29e] [CorrID:75a09b80-8b81-47a1-8a6b-91c12794c399] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=346, TaskID=ApiRequestNode-node-execution-request-0-346-1748522939.8225026
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=347, TaskID=ApiRequestNode-node-execution-request-0-347-1748523039.0954478
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-347-1748523039.0954478, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "d7aa3f75-76dd-41ac-8393-38ffba611af5",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:713] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool ApiRequestNode for RequestID=d7aa3f75-76dd-41ac-8393-38ffba611af5, TaskID=ApiRequestNode-node-execution-request-0-347-1748523039.0954478
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=348, TaskID=ApiRequestNode-node-execution-request-0-348-1748523039.1007626
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-348-1748523039.1007626, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "087bb3c2-28fc-4332-bd79-4961b43439c5",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:713] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool CombineTextComponent for RequestID=087bb3c2-28fc-4332-bd79-4961b43439c5, TaskID=ApiRequestNode-node-execution-request-0-348-1748523039.1007626
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:717] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool CombineTextComponent executed successfully for RequestID=087bb3c2-28fc-4332-bd79-4961b43439c5, TaskID=ApiRequestNode-node-execution-request-0-348-1748523039.1007626
2025-05-29 18:20:39 - ComponentSystem - INFO - [_send_result:1005] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Preparing to send result for component ApiRequestNode, RequestID=087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:39 - ComponentSystem - INFO - [_send_result:1077] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sending Kafka response: RequestID=087bb3c2-28fc-4332-bd79-4961b43439c5, Response={
  "request_id": "087bb3c2-28fc-4332-bd79-4961b43439c5",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748523039.372444,
  "result": {
    "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
  },
  "error": null
}
2025-05-29 18:20:39 - ComponentSystem - INFO - [_send_result:1086] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sent result for component ApiRequestNode to topic node_results for RequestID=087bb3c2-28fc-4332-bd79-4961b43439c5
2025-05-29 18:20:39 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Successfully committed offset 349 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-348-1748523039.1007626
2025-05-29 18:20:39 - ComponentSystem - INFO - [_process_message:936] [ReqID:087bb3c2-28fc-4332-bd79-4961b43439c5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=348, TaskID=ApiRequestNode-node-execution-request-0-348-1748523039.1007626
2025-05-29 18:20:40 - ComponentSystem - INFO - [_process_message:717] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool ApiRequestNode executed successfully for RequestID=d7aa3f75-76dd-41ac-8393-38ffba611af5, TaskID=ApiRequestNode-node-execution-request-0-347-1748523039.0954478
2025-05-29 18:20:40 - ComponentSystem - INFO - [_send_result:1005] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Preparing to send result for component ApiRequestNode, RequestID=d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ComponentSystem - INFO - [_send_result:1077] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sending Kafka response: RequestID=d7aa3f75-76dd-41ac-8393-38ffba611af5, Response={
  "request_id": "d7aa3f75-76dd-41ac-8393-38ffba611af5",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748523040.1837342,
  "result": {
    "status_code": 200,
    "headers": {
      "Date": "Thu, 29 May 2025 12:50:39 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-sA10PMv4R9nhyYlingeTtkRLWiM\"",
      "X-Response-Time": "0.68439ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=lYZIivwAHt3FWpa68xism7sDovBzB%2FGiyMALVljer5ecCIDy%2Bycc8Xmr0ESbLzYEM4hb%2BEppiajJ9KoxxxYhvcS0K4SRNfSw%2BAEjkvoF8Hep4YuWYpA%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "94761e640eb6f7ba-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "content_type": "text/plain",
    "data": "1748523039658-1119329254142",
    "method": "POST",
    "url": "https://www.postb.in/1748522981790-7621122791897"
  },
  "error": null
}
2025-05-29 18:20:40 - ComponentSystem - INFO - [_send_result:1086] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sent result for component ApiRequestNode to topic node_results for RequestID=d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Successfully committed offset 348 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-347-1748523039.0954478
2025-05-29 18:20:40 - ComponentSystem - INFO - [_process_message:936] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=347, TaskID=ApiRequestNode-node-execution-request-0-347-1748523039.0954478
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=349, TaskID=ApiRequestNode-node-execution-request-0-349-1748523048.3064604
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-349-1748523048.3064604, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "e9348348-fcda-4afa-8907-1b38147bdaf6",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:713] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool MergeDataComponent for RequestID=e9348348-fcda-4afa-8907-1b38147bdaf6, TaskID=ApiRequestNode-node-execution-request-0-349-1748523048.3064604
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:717] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool MergeDataComponent executed successfully for RequestID=e9348348-fcda-4afa-8907-1b38147bdaf6, TaskID=ApiRequestNode-node-execution-request-0-349-1748523048.3064604
2025-05-29 18:20:48 - ComponentSystem - INFO - [_send_result:1005] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Preparing to send result for component ApiRequestNode, RequestID=e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ComponentSystem - INFO - [_send_result:1037] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Component returned error status for RequestID=e9348348-fcda-4afa-8907-1b38147bdaf6: Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 18:20:48 - ComponentSystem - INFO - [_send_result:1077] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sending Kafka response: RequestID=e9348348-fcda-4afa-8907-1b38147bdaf6, Response={
  "request_id": "e9348348-fcda-4afa-8907-1b38147bdaf6",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748523048.3231878,
  "result": null,
  "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
}
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=350, TaskID=ApiRequestNode-node-execution-request-0-350-1748523048.3064604
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-350-1748523048.3064604, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "46ec8cd9-4d77-4f21-bbe8-c55c7e231620",
  "correlation_id": "49813ee3-be88-4752-93df-e9b5850a89c6"
}
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:713] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Executing tool ApiRequestNode for RequestID=46ec8cd9-4d77-4f21-bbe8-c55c7e231620, TaskID=ApiRequestNode-node-execution-request-0-350-1748523048.3064604
2025-05-29 18:20:48 - ComponentSystem - INFO - [_send_result:1086] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sent result for component ApiRequestNode to topic node_results for RequestID=e9348348-fcda-4afa-8907-1b38147bdaf6
2025-05-29 18:20:48 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Successfully committed offset 350 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-349-1748523048.3064604
2025-05-29 18:20:48 - ComponentSystem - INFO - [_process_message:936] [ReqID:e9348348-fcda-4afa-8907-1b38147bdaf6] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=349, TaskID=ApiRequestNode-node-execution-request-0-349-1748523048.3064604
2025-05-29 18:20:49 - ComponentSystem - INFO - [_process_message:717] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Tool ApiRequestNode executed successfully for RequestID=46ec8cd9-4d77-4f21-bbe8-c55c7e231620, TaskID=ApiRequestNode-node-execution-request-0-350-1748523048.3064604
2025-05-29 18:20:49 - ComponentSystem - INFO - [_send_result:1005] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Preparing to send result for component ApiRequestNode, RequestID=46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ComponentSystem - INFO - [_send_result:1077] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sending Kafka response: RequestID=46ec8cd9-4d77-4f21-bbe8-c55c7e231620, Response={
  "request_id": "46ec8cd9-4d77-4f21-bbe8-c55c7e231620",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748523049.0455117,
  "result": {
    "status_code": 200,
    "headers": {
      "Date": "Thu, 29 May 2025 12:50:48 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-cBv1LNS/lwVdbnkKCbRLWBu3+O4\"",
      "X-Response-Time": "0.63523ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=3kqpXlUwr9NY0stcWFKgcDH2g9OudnFZIhSUKv7ItY3PzOloLpB9jGJVFt6o7CdyfIsTFW70%2Fbr1hqRWhhuRjijqQsd2NYWh7NXU0mMcDg8y5GACKeaWew%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "94761e9b9ff4e197-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "content_type": "text/plain",
    "data": "1748523048536-8838748724665",
    "method": "POST",
    "url": "https://www.postb.in/1748522981790-7621122791897"
  },
  "error": null
}
2025-05-29 18:20:49 - ComponentSystem - INFO - [_send_result:1086] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Sent result for component ApiRequestNode to topic node_results for RequestID=46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Successfully committed offset 351 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-350-1748523048.3064604
2025-05-29 18:20:49 - ComponentSystem - INFO - [_process_message:936] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=350, TaskID=ApiRequestNode-node-execution-request-0-350-1748523048.3064604
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=351, TaskID=ApiRequestNode-node-execution-request-0-351-1748523186.665474
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-351-1748523186.665474, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "3267d8bb-feef-42c6-8be9-4fee612c841e",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:713] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool ApiRequestNode for RequestID=3267d8bb-feef-42c6-8be9-4fee612c841e, TaskID=ApiRequestNode-node-execution-request-0-351-1748523186.665474
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=352, TaskID=ApiRequestNode-node-execution-request-0-352-1748523186.6678464
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-352-1748523186.6678464, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "04bc3144-7941-4b41-8241-17d832c66a02",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:713] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool CombineTextComponent for RequestID=04bc3144-7941-4b41-8241-17d832c66a02, TaskID=ApiRequestNode-node-execution-request-0-352-1748523186.6678464
2025-05-29 18:23:06 - ComponentSystem - INFO - [_process_message:717] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool CombineTextComponent executed successfully for RequestID=04bc3144-7941-4b41-8241-17d832c66a02, TaskID=ApiRequestNode-node-execution-request-0-352-1748523186.6678464
2025-05-29 18:23:06 - ComponentSystem - INFO - [_send_result:1005] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Preparing to send result for component ApiRequestNode, RequestID=04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:06 - ComponentSystem - INFO - [_send_result:1077] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sending Kafka response: RequestID=04bc3144-7941-4b41-8241-17d832c66a02, Response={
  "request_id": "04bc3144-7941-4b41-8241-17d832c66a02",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748523186.756689,
  "result": {
    "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
  },
  "error": null
}
2025-05-29 18:23:07 - ComponentSystem - INFO - [_send_result:1086] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sent result for component ApiRequestNode to topic node_results for RequestID=04bc3144-7941-4b41-8241-17d832c66a02
2025-05-29 18:23:07 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully committed offset 353 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-352-1748523186.6678464
2025-05-29 18:23:07 - ComponentSystem - INFO - [_process_message:936] [ReqID:04bc3144-7941-4b41-8241-17d832c66a02] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=352, TaskID=ApiRequestNode-node-execution-request-0-352-1748523186.6678464
2025-05-29 18:23:07 - ComponentSystem - INFO - [_process_message:717] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool ApiRequestNode executed successfully for RequestID=3267d8bb-feef-42c6-8be9-4fee612c841e, TaskID=ApiRequestNode-node-execution-request-0-351-1748523186.665474
2025-05-29 18:23:07 - ComponentSystem - INFO - [_send_result:1005] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Preparing to send result for component ApiRequestNode, RequestID=3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:07 - ComponentSystem - INFO - [_send_result:1077] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sending Kafka response: RequestID=3267d8bb-feef-42c6-8be9-4fee612c841e, Response={
  "request_id": "3267d8bb-feef-42c6-8be9-4fee612c841e",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748523187.5377922,
  "result": {
    "status_code": 200,
    "headers": {
      "Date": "Thu, 29 May 2025 12:53:07 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-jXkF9QaK7yAHTbgTbHMbvDWVq2k\"",
      "X-Response-Time": "0.47623ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=hcR5LDvZeHbOtn3O18b23UKND0Mj2BwYjdJlTAVR2IWRynGMy4dIG0rVSkw20%2FWdQ1X%2BV%2BRB1tPuMTHk3%2BDQgAHHkc%2BBbxgZGSG0OS5he9GhU0dI4DsNnQ%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "947621fd3d20e218-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "content_type": "text/plain",
    "data": "1748523187035-7723457510583",
    "method": "POST",
    "url": "https://www.postb.in/1748522981790-7621122791897"
  },
  "error": null
}
2025-05-29 18:23:07 - ComponentSystem - INFO - [_send_result:1086] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sent result for component ApiRequestNode to topic node_results for RequestID=3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:08 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully committed offset 352 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-351-1748523186.665474
2025-05-29 18:23:08 - ComponentSystem - INFO - [_process_message:936] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=351, TaskID=ApiRequestNode-node-execution-request-0-351-1748523186.665474
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=353, TaskID=ApiRequestNode-node-execution-request-0-353-1748523194.9422998
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-353-1748523194.9422998, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748522981790-7621122791897",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "eca069e4-6916-485a-a9d1-9f31358f16c2",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:713] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool ApiRequestNode for RequestID=eca069e4-6916-485a-a9d1-9f31358f16c2, TaskID=ApiRequestNode-node-execution-request-0-353-1748523194.9422998
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=354, TaskID=ApiRequestNode-node-execution-request-0-354-1748523194.9422998
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-354-1748523194.9422998, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "3e085dac-1e65-4de4-a1e6-952d7a69e387",
  "correlation_id": "877163cd-506c-4646-b88f-302f99699a91"
}
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:713] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Executing tool MergeDataComponent for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387, TaskID=ApiRequestNode-node-execution-request-0-354-1748523194.9422998
2025-05-29 18:23:14 - ComponentSystem - INFO - [_process_message:717] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool MergeDataComponent executed successfully for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387, TaskID=ApiRequestNode-node-execution-request-0-354-1748523194.9422998
2025-05-29 18:23:14 - ComponentSystem - INFO - [_send_result:1005] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Preparing to send result for component ApiRequestNode, RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:14 - ComponentSystem - INFO - [_send_result:1037] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Component returned error status for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387: Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 18:23:14 - ComponentSystem - INFO - [_send_result:1077] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sending Kafka response: RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387, Response={
  "request_id": "3e085dac-1e65-4de4-a1e6-952d7a69e387",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748523194.9736447,
  "result": null,
  "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
}
2025-05-29 18:23:15 - ComponentSystem - INFO - [_send_result:1086] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sent result for component ApiRequestNode to topic node_results for RequestID=3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:23:15 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully committed offset 355 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-354-1748523194.9422998
2025-05-29 18:23:15 - ComponentSystem - INFO - [_process_message:936] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=354, TaskID=ApiRequestNode-node-execution-request-0-354-1748523194.9422998
2025-05-29 18:23:15 - ComponentSystem - INFO - [_process_message:717] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Tool ApiRequestNode executed successfully for RequestID=eca069e4-6916-485a-a9d1-9f31358f16c2, TaskID=ApiRequestNode-node-execution-request-0-353-1748523194.9422998
2025-05-29 18:23:15 - ComponentSystem - INFO - [_send_result:1005] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Preparing to send result for component ApiRequestNode, RequestID=eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:15 - ComponentSystem - INFO - [_send_result:1077] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sending Kafka response: RequestID=eca069e4-6916-485a-a9d1-9f31358f16c2, Response={
  "request_id": "eca069e4-6916-485a-a9d1-9f31358f16c2",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748523195.6516335,
  "result": {
    "status_code": 200,
    "headers": {
      "Date": "Thu, 29 May 2025 12:53:15 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-8ood61lbZXlQdG7Dh2rQwPtwrCw\"",
      "X-Response-Time": "0.51622ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=VxXF61cwuYYHR%2BMBWTnW%2FAg98KSsqWEQkbt3AOHfkO%2F7QUlMPazCyHpcfsfQ9QRSeSLVYbCIpV7GFpLv3uB8w6CKTcPEvqIp6igVvTlAKFm%2BUu4ZLJ8%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9476222fec787b11-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "content_type": "text/plain",
    "data": "1748523195156-8719265519175",
    "method": "POST",
    "url": "https://www.postb.in/1748522981790-7621122791897"
  },
  "error": null
}
2025-05-29 18:23:15 - ComponentSystem - INFO - [_send_result:1086] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Sent result for component ApiRequestNode to topic node_results for RequestID=eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:16 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully committed offset 354 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-353-1748523194.9422998
2025-05-29 18:23:16 - ComponentSystem - INFO - [_process_message:936] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=353, TaskID=ApiRequestNode-node-execution-request-0-353-1748523194.9422998
2025-05-29 18:35:35 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-29 18:35:35 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-29 18:35:35 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-29 18:35:35 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
