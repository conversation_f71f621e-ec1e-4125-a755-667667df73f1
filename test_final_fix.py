#!/usr/bin/env python3
"""
Final test to verify that conditional field requirements are working.
"""
import requests
import json
import time

def test_conditional_field_requirements():
    """Test that the API Request component has conditional field requirements."""
    print("🔍 TESTING CONDITIONAL FIELD REQUIREMENTS FIX")
    print("=" * 80)

    # Wait for services to start
    print("Waiting for services to fully start...")
    time.sleep(3)

    try:
        # Make request to components endpoint
        url = "http://localhost:8000/api/v1/components"
        print(f"Making request to: {url}")

        response = requests.get(url, timeout=30)

        if response.status_code != 200:
            print(f"❌ ERROR: API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False

        print("✅ API request successful")

        # Parse response
        try:
            components = response.json()
        except Exception as e:
            print(f"❌ ERROR: Failed to parse JSON response: {e}")
            print(f"Response content: {response.text[:500]}...")
            return False

        # Find ApiRequestNode
        api_request_component = None

        # Handle nested structure: {category: {component_name: component_data}}
        if isinstance(components, dict):
            for category, category_components in components.items():
                if isinstance(category_components, dict):
                    for component_name, component_data in category_components.items():
                        if component_name == "ApiRequestNode":
                            api_request_component = component_data
                            break
                if api_request_component:
                    break
        else:
            # Handle flat array structure
            for component in components:
                if component.get("name") == "ApiRequestNode":
                    api_request_component = component
                    break

        if not api_request_component:
            print("❌ ApiRequestNode not found")
            return False

        print("✅ Found ApiRequestNode")

        # Find body input
        body_input = None
        for input_def in api_request_component.get("inputs", []):
            if input_def.get("name") == "body":
                body_input = input_def
                break

        if not body_input:
            print("❌ Body input not found")
            return False

        print("✅ Found body input")

        # Check requirement rules
        print("\n📋 Body Input Analysis:")
        print(f"  Name: {body_input.get('name')}")
        print(f"  Display Name: {body_input.get('display_name')}")
        print(f"  Required: {body_input.get('required')}")
        print(f"  Has visibility_rules: {body_input.get('visibility_rules') is not None}")
        print(f"  Has requirement_rules: {body_input.get('requirement_rules') is not None}")
        print(f"  Requirement logic: {body_input.get('requirement_logic')}")

        # Check if requirement_rules exist and are not empty
        requirement_rules = body_input.get("requirement_rules")
        if requirement_rules is None:
            print("\n❌ CONDITIONAL FIELD REQUIREMENTS MISSING!")
            print("The requirement_rules field is missing or null")
            return False

        if not isinstance(requirement_rules, list) or len(requirement_rules) == 0:
            print("\n❌ CONDITIONAL FIELD REQUIREMENTS EMPTY!")
            print("The requirement_rules field is empty")
            return False

        print(f"\n✅ CONDITIONAL FIELD REQUIREMENTS FOUND!")
        print(f"Number of requirement rules: {len(requirement_rules)}")

        # Verify the rules
        expected_values = ["POST", "PUT", "PATCH"]
        found_values = []

        for i, rule in enumerate(requirement_rules):
            field_name = rule.get("field_name")
            field_value = rule.get("field_value")
            operator = rule.get("operator", "equals")

            print(f"  Rule {i+1}: {field_name} {operator} {field_value}")

            if field_name == "method" and field_value in expected_values:
                found_values.append(field_value)

        # Check if all expected values are found
        missing_values = set(expected_values) - set(found_values)
        if missing_values:
            print(f"\n❌ MISSING REQUIREMENT RULES!")
            print(f"Missing values: {missing_values}")
            return False

        print(f"\n✅ ALL REQUIREMENT RULES VERIFIED!")
        print(f"Found all expected method values: {sorted(found_values)}")

        # Check requirement logic
        requirement_logic = body_input.get("requirement_logic")
        if requirement_logic != "OR":
            print(f"\n⚠️  WARNING: Expected requirement_logic 'OR', got '{requirement_logic}'")
        else:
            print(f"\n✅ REQUIREMENT LOGIC CORRECT: {requirement_logic}")

        return True

    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_conditional_field_requirements()

    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    if success:
        print("🎉 CONDITIONAL FIELD REQUIREMENTS TEST PASSED!")
        print("✅ requirement_rules are properly implemented and working")
        print("✅ API Request component body input has conditional requirements")
        print("✅ Requirements are triggered when method is POST, PUT, or PATCH")
    else:
        print("❌ CONDITIONAL FIELD REQUIREMENTS TEST FAILED!")
        print("⚠️  requirement_rules are missing or incorrect")
        print("💡 Check the implementation and try again")

    exit(0 if success else 1)
