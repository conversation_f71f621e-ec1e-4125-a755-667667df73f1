#!/usr/bin/env python3
"""
Test script to call the components endpoint and check for conditional field requirements.
"""
import requests
import json
import time

def test_components_endpoint():
    """Test the components endpoint for conditional field requirements."""
    print("🔍 TESTING COMPONENTS ENDPOINT")
    print("=" * 80)

    # Wait a bit for services to fully start
    print("Waiting for services to fully start...")
    time.sleep(10)

    try:
        # Make request to components endpoint
        url = "http://localhost:8000/api/v1/components"
        print(f"Making request to: {url}")

        response = requests.get(url, timeout=60)

        if response.status_code != 200:
            print(f"❌ ERROR: API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False

        print("✅ API request successful")

        # Parse JSON response
        data = response.json()

        # Find API Request component
        api_request_component = None
        for category, components in data.items():
            if isinstance(components, dict):
                for component_name, component_data in components.items():
                    if component_name == "ApiRequestNode":
                        api_request_component = component_data
                        break
            if api_request_component:
                break

        if not api_request_component:
            print("❌ ERROR: ApiRequestNode not found")
            return False

        print("✅ Found ApiRequestNode")

        # Find body input
        body_input = None
        for input_def in api_request_component.get("inputs", []):
            if input_def.get("name") == "body":
                body_input = input_def
                break

        if not body_input:
            print("❌ ERROR: Body input not found")
            return False

        print("✅ Found body input")

        # Check for conditional requirements
        requirement_rules = body_input.get("requirement_rules")
        requirement_logic = body_input.get("requirement_logic")

        print(f"\n📋 Body Input Analysis:")
        print(f"  Name: {body_input.get('name')}")
        print(f"  Display Name: {body_input.get('display_name')}")
        print(f"  Required: {body_input.get('required')}")
        print(f"  Has visibility_rules: {bool(body_input.get('visibility_rules'))}")
        print(f"  Has requirement_rules: {bool(requirement_rules)}")
        print(f"  Requirement logic: {requirement_logic}")

        if not requirement_rules:
            print("\n❌ CONDITIONAL FIELD REQUIREMENTS MISSING!")
            print("The requirement_rules field is missing or null")

            # Save response for debugging
            with open("debug_components_response.json", "w") as f:
                json.dump(api_request_component, f, indent=2)
            print("📁 Saved component response to debug_components_response.json")

            return False

        print(f"\n✅ SUCCESS: Found {len(requirement_rules)} requirement rules!")

        # Verify requirement rules content
        expected_methods = {"POST", "PUT", "PATCH"}
        actual_methods = set()

        print("\n📋 Requirement Rules Details:")
        for i, rule in enumerate(requirement_rules, 1):
            field_name = rule.get("field_name")
            field_value = rule.get("field_value")
            operator = rule.get("operator", "equals")

            print(f"  {i}. {field_name} {operator} {field_value}")

            if field_name == "method":
                actual_methods.add(field_value)

        if actual_methods != expected_methods:
            print(f"\n❌ ERROR: Expected methods {expected_methods}, got {actual_methods}")
            return False

        print(f"\n✅ Requirement rules cover correct methods: {actual_methods}")

        # Verify requirement logic
        if requirement_logic != "OR":
            print(f"❌ ERROR: Expected requirement logic 'OR', got '{requirement_logic}'")
            return False

        print("✅ Requirement logic is correctly set to 'OR'")

        # Save successful response
        with open("successful_components_response.json", "w") as f:
            json.dump(api_request_component, f, indent=2)
        print("📁 Saved successful response to successful_components_response.json")

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    success = test_components_endpoint()

    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    if success:
        print("🎉 CONDITIONAL FIELD REQUIREMENTS TEST PASSED!")
        print("✅ requirement_rules are properly included in the API response")
        print("✅ Body field will be required for POST/PUT/PATCH methods")
        print("✅ Frontend validation will work correctly")
        print("✅ Saved workflows will include conditional requirements")
    else:
        print("❌ CONDITIONAL FIELD REQUIREMENTS TEST FAILED!")
        print("⚠️  requirement_rules are missing from the API response")
        print("💡 Check the debug files for detailed analysis")

    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
