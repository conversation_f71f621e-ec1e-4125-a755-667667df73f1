2025-05-29 18:17:50 - ApiRequestNode - INFO - [setup_logger:467] Logger ApiRequestNode configured with log file: logs\2025-05-29\ApiRequestNode_18-17-50.log
2025-05-29 18:17:50 - ApiRequestNode - INFO - [__init__:64] Initializing API Component
2025-05-29 18:17:50 - ApiRequestNode - INFO - [__init__:67] API Component initialized successfully
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:206] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Starting API request processing for request_id: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:224] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Extracting request new parameters https://www.postb.in/1748522981790-7621122791897, POST, {}, {} for request_id d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:232] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:234] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:240] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:260] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Final request body values - raw: {'technical': '12'}, json: None for request_id d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:267] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Request parameters extracted for request_id d7aa3f75-76dd-41ac-8393-38ffba611af5: URL=https://www.postb.in/1748522981790-7621122791897, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:345] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748522981790-7621122791897, Timeout: Nones, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:361] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:391] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748522981790-7621122791897 RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:392] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP BODY] None
2025-05-29 18:20:39 - ApiRequestNode - INFO - [process:393] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 18:20:40 - ApiRequestNode - INFO - [process:409] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP REQUEST COMPLETED] Duration: 0.927s, Status: 200, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ApiRequestNode - INFO - [process:414] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748522981790-7621122791897, Method: POST, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5 
2025-05-29 18:20:40 - ApiRequestNode - INFO - [process:419] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:50:39 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-sA10PMv4R9nhyYlingeTtkRLWiM\"",
  "X-Response-Time": "0.68439ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=lYZIivwAHt3FWpa68xism7sDovBzB%2FGiyMALVljer5ecCIDy%2Bycc8Xmr0ESbLzYEM4hb%2BEppiajJ9KoxxxYhvcS0K4SRNfSw%2BAEjkvoF8Hep4YuWYpA%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "94761e640eb6f7ba-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ApiRequestNode - INFO - [process:430] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ApiRequestNode - INFO - [process:588] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE BODY] Text: 1748523039658-1119329254142, RequestID: d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:40 - ApiRequestNode - INFO - [process:598] [ReqID:d7aa3f75-76dd-41ac-8393-38ffba611af5] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] API request successful: Status=200, RequestID=d7aa3f75-76dd-41ac-8393-38ffba611af5
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:206] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Starting API request processing for request_id: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:224] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Extracting request new parameters https://www.postb.in/1748522981790-7621122791897, POST, {}, {} for request_id 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:232] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:234] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:240] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:260] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Final request body values - raw: {'technical': '12'}, json: None for request_id 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:267] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] Request parameters extracted for request_id 46ec8cd9-4d77-4f21-bbe8-c55c7e231620: URL=https://www.postb.in/1748522981790-7621122791897, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:345] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748522981790-7621122791897, Timeout: Nones, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:361] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:391] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748522981790-7621122791897 RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:392] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP BODY] None
2025-05-29 18:20:48 - ApiRequestNode - INFO - [process:393] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 18:20:49 - ApiRequestNode - INFO - [process:409] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP REQUEST COMPLETED] Duration: 0.697s, Status: 200, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ApiRequestNode - INFO - [process:414] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748522981790-7621122791897, Method: POST, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620 
2025-05-29 18:20:49 - ApiRequestNode - INFO - [process:419] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:50:48 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-cBv1LNS/lwVdbnkKCbRLWBu3+O4\"",
  "X-Response-Time": "0.63523ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=3kqpXlUwr9NY0stcWFKgcDH2g9OudnFZIhSUKv7ItY3PzOloLpB9jGJVFt6o7CdyfIsTFW70%2Fbr1hqRWhhuRjijqQsd2NYWh7NXU0mMcDg8y5GACKeaWew%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "94761e9b9ff4e197-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ApiRequestNode - INFO - [process:430] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ApiRequestNode - INFO - [process:588] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] [HTTP RESPONSE BODY] Text: 1748523048536-8838748724665, RequestID: 46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:20:49 - ApiRequestNode - INFO - [process:598] [ReqID:46ec8cd9-4d77-4f21-bbe8-c55c7e231620] [CorrID:49813ee3-be88-4752-93df-e9b5850a89c6] API request successful: Status=200, RequestID=46ec8cd9-4d77-4f21-bbe8-c55c7e231620
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:206] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Starting API request processing for request_id: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:224] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Extracting request new parameters https://www.postb.in/1748522981790-7621122791897, POST, {}, {} for request_id 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:232] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:234] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:240] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:260] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Final request body values - raw: {'technical': '12'}, json: None for request_id 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:267] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Request parameters extracted for request_id 3267d8bb-feef-42c6-8be9-4fee612c841e: URL=https://www.postb.in/1748522981790-7621122791897, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:345] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748522981790-7621122791897, Timeout: Nones, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:361] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:391] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748522981790-7621122791897 RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:392] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP BODY] None
2025-05-29 18:23:06 - ApiRequestNode - INFO - [process:393] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 18:23:07 - ApiRequestNode - INFO - [process:409] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP REQUEST COMPLETED] Duration: 0.803s, Status: 200, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:07 - ApiRequestNode - INFO - [process:414] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748522981790-7621122791897, Method: POST, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e 
2025-05-29 18:23:07 - ApiRequestNode - INFO - [process:419] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:53:07 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-jXkF9QaK7yAHTbgTbHMbvDWVq2k\"",
  "X-Response-Time": "0.47623ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=hcR5LDvZeHbOtn3O18b23UKND0Mj2BwYjdJlTAVR2IWRynGMy4dIG0rVSkw20%2FWdQ1X%2BV%2BRB1tPuMTHk3%2BDQgAHHkc%2BBbxgZGSG0OS5he9GhU0dI4DsNnQ%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "947621fd3d20e218-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:07 - ApiRequestNode - INFO - [process:430] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:07 - ApiRequestNode - INFO - [process:588] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE BODY] Text: 1748523187035-7723457510583, RequestID: 3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:07 - ApiRequestNode - INFO - [process:598] [ReqID:3267d8bb-feef-42c6-8be9-4fee612c841e] [CorrID:877163cd-506c-4646-b88f-302f99699a91] API request successful: Status=200, RequestID=3267d8bb-feef-42c6-8be9-4fee612c841e
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:206] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Starting API request processing for request_id: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:224] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Extracting request new parameters https://www.postb.in/1748522981790-7621122791897, POST, {}, {} for request_id eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:232] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:234] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:240] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:260] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Final request body values - raw: {'technical': '12'}, json: None for request_id eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:267] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Request parameters extracted for request_id eca069e4-6916-485a-a9d1-9f31358f16c2: URL=https://www.postb.in/1748522981790-7621122791897, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:345] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748522981790-7621122791897, Timeout: Nones, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:361] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:391] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748522981790-7621122791897 RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:392] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP BODY] None
2025-05-29 18:23:14 - ApiRequestNode - INFO - [process:393] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 18:23:15 - ApiRequestNode - INFO - [process:409] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP REQUEST COMPLETED] Duration: 0.691s, Status: 200, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:15 - ApiRequestNode - INFO - [process:414] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748522981790-7621122791897, Method: POST, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2 
2025-05-29 18:23:15 - ApiRequestNode - INFO - [process:419] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:53:15 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-8ood61lbZXlQdG7Dh2rQwPtwrCw\"",
  "X-Response-Time": "0.51622ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=VxXF61cwuYYHR%2BMBWTnW%2FAg98KSsqWEQkbt3AOHfkO%2F7QUlMPazCyHpcfsfQ9QRSeSLVYbCIpV7GFpLv3uB8w6CKTcPEvqIp6igVvTlAKFm%2BUu4ZLJ8%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9476222fec787b11-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:15 - ApiRequestNode - INFO - [process:430] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:15 - ApiRequestNode - INFO - [process:588] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] [HTTP RESPONSE BODY] Text: 1748523195156-8719265519175, RequestID: eca069e4-6916-485a-a9d1-9f31358f16c2
2025-05-29 18:23:15 - ApiRequestNode - INFO - [process:598] [ReqID:eca069e4-6916-485a-a9d1-9f31358f16c2] [CorrID:877163cd-506c-4646-b88f-302f99699a91] API request successful: Status=200, RequestID=eca069e4-6916-485a-a9d1-9f31358f16c2
