#!/usr/bin/env python3
"""
Test script to verify that the API Gateway properly includes requirement_rules
in the components discovery response.
"""
import requests
import json
import sys

def test_api_gateway_components():
    """Test the API Gateway components discovery endpoint."""
    print("=" * 80)
    print("TESTING API GATEWAY COMPONENTS DISCOVERY")
    print("=" * 80)

    try:
        # Make request to API Gateway components endpoint
        url = "http://localhost:8000/api/v1/components"
        print(f"Making request to: {url}")

        response = requests.get(url, timeout=10)

        if response.status_code != 200:
            print(f"❌ ERROR: API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False

        print("✓ API request successful")

        # Parse JSON response
        try:
            data = response.json()
        except json.JSONDecodeError as e:
            print(f"❌ ERROR: Failed to parse JSON response: {e}")
            return False

        print("✓ JSON response parsed successfully")

        # Find API Request component
        api_request_component = None

        # Navigate through the nested structure
        for category, components in data.items():
            if isinstance(components, dict):
                for component_name, component_data in components.items():
                    if component_name == "ApiRequestNode":
                        api_request_component = component_data
                        break
            if api_request_component:
                break

        if api_request_component is None:
            print("❌ ERROR: ApiRequestNode not found in API response")
            print("Available components:")
            for category, components in data.items():
                if isinstance(components, dict):
                    for component_name in components.keys():
                        print(f"  - {component_name}")
            return False

        print("✓ Found ApiRequestNode in API response")

        # Find body input
        body_input = None
        inputs = api_request_component.get("inputs", [])

        for input_def in inputs:
            if input_def.get("name") == "body":
                body_input = input_def
                break

        if body_input is None:
            print("❌ ERROR: Body input not found in API response")
            print("Available inputs:")
            for input_def in inputs:
                print(f"  - {input_def.get('name', 'unknown')}")
            return False

        print("✓ Found body input in API response")

        # Check for requirement_rules
        requirement_rules = body_input.get("requirement_rules")
        requirement_logic = body_input.get("requirement_logic")

        print(f"\n📋 Body Input Analysis:")
        print(f"  Name: {body_input.get('name')}")
        print(f"  Display Name: {body_input.get('display_name')}")
        print(f"  Required: {body_input.get('required')}")
        print(f"  Has visibility_rules: {bool(body_input.get('visibility_rules'))}")
        print(f"  Has requirement_rules: {bool(requirement_rules)}")
        print(f"  Requirement logic: {requirement_logic}")

        if not requirement_rules:
            print("\n❌ ERROR: requirement_rules missing from API response")
            print("Available fields in body input:")
            for key in sorted(body_input.keys()):
                print(f"  - {key}: {type(body_input[key]).__name__}")
            return False

        print(f"✓ Body input has {len(requirement_rules)} requirement rules")

        # Verify requirement rules content
        expected_methods = {"POST", "PUT", "PATCH"}
        actual_methods = set()

        print("\n📋 Requirement Rules Details:")
        for i, rule in enumerate(requirement_rules, 1):
            field_name = rule.get("field_name")
            field_value = rule.get("field_value")
            operator = rule.get("operator", "equals")

            print(f"  {i}. Field: {field_name}")
            print(f"     Value: {field_value}")
            print(f"     Operator: {operator}")

            if field_name == "method":
                actual_methods.add(field_value)

        if actual_methods != expected_methods:
            print(f"\n❌ ERROR: Expected methods {expected_methods}, got {actual_methods}")
            return False

        print(f"\n✓ Requirement rules cover correct methods: {actual_methods}")

        # Verify requirement logic
        if requirement_logic != "OR":
            print(f"❌ ERROR: Expected requirement logic 'OR', got '{requirement_logic}'")
            return False

        print("✓ Requirement logic is correctly set to 'OR'")

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_response_for_analysis():
    """Save the API response to a file for analysis."""
    print("\n" + "=" * 80)
    print("SAVING API RESPONSE FOR ANALYSIS")
    print("=" * 80)

    try:
        url = "http://localhost:8000/api/v1/components"
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            # Save full response
            with open("api_components_response.json", "w") as f:
                json.dump(response.json(), f, indent=2)
            print("✓ Full API response saved to api_components_response.json")

            # Extract and save just the API Request component
            data = response.json()
            api_request_component = None

            for category, components in data.items():
                if isinstance(components, dict):
                    for component_name, component_data in components.items():
                        if component_name == "ApiRequestNode":
                            api_request_component = component_data
                            break
                if api_request_component:
                    break

            if api_request_component:
                with open("api_request_component_response.json", "w") as f:
                    json.dump(api_request_component, f, indent=2)
                print("✓ API Request component saved to api_request_component_response.json")

            return True
        else:
            print(f"❌ ERROR: API request failed with status {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ ERROR: Failed to save response: {e}")
        return False

def main():
    """Run the API Gateway test."""
    print("🔍 TESTING API GATEWAY CONDITIONAL FIELD REQUIREMENTS")
    print("=" * 80)
    print("This script tests whether the API Gateway properly includes")
    print("requirement_rules in the components discovery response.")
    print("=" * 80)

    # Test API Gateway response
    api_test_passed = test_api_gateway_components()

    # Save response for analysis
    save_response_for_analysis()

    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    if api_test_passed:
        print("🎉 API GATEWAY TEST PASSED!")
        print("✅ requirement_rules are properly included in the API response")
        print("✅ Conditional field requirements are working end-to-end")
    else:
        print("❌ API GATEWAY TEST FAILED!")
        print("⚠️  requirement_rules are missing from the API response")
        print("💡 Check the saved response files for detailed analysis")

    return api_test_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
