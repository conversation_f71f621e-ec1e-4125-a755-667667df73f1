2025-05-29 18:35:46 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-29\MergeDataComponent_18-35-46.log
2025-05-29 18:35:52 - Merge<PERSON><PERSON>Component - INFO - [__init__:96] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] MergeDataExecutor initialized
2025-05-29 18:35:52 - MergeDataComponent - INFO - [process:241] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Processing merge data request for request_id: 3e085dac-1e65-4de4-a1e6-952d7a69e387
2025-05-29 18:35:52 - MergeDataComponent - INFO - [process:243] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:35:52 - MergeDataComponent - INFO - [process:257] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:35:52 - MergeDataComponent - INFO - [process:277] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Merging data for request_id 3e085dac-1e65-4de4-a1e6-952d7a69e387. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 18:35:52 - MergeDataComponent - ERROR - [process:308] [ReqID:3e085dac-1e65-4de4-a1e6-952d7a69e387] [CorrID:877163cd-506c-4646-b88f-302f99699a91] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 18:37:13 - MergeDataComponent - INFO - [process:241] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Processing merge data request for request_id: 5bcf3662-e4f8-4584-a5e6-1304252b5aa4
2025-05-29 18:37:13 - MergeDataComponent - INFO - [process:243] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:37:13 - MergeDataComponent - INFO - [process:257] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 18:37:13 - MergeDataComponent - INFO - [process:277] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Merging data for request_id 5bcf3662-e4f8-4584-a5e6-1304252b5aa4. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 18:37:13 - MergeDataComponent - ERROR - [process:308] [ReqID:5bcf3662-e4f8-4584-a5e6-1304252b5aa4] [CorrID:89e274bb-35e0-4aad-ae09-ce80cb48aaa2] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
